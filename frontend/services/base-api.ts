// Standardized API response format from backend
interface ApiResponse<T = any> {
  status: 'success' | 'error';
  http_code: number;
  message: string;
  data?: T;
}

interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
}

export class BaseApiService {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseUrl: string = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001/api') {
    this.baseUrl = baseUrl;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }

  private async request<T>(endpoint: string, config: RequestConfig = {}): Promise<ApiResponse<T>> {
    const { method = 'GET', headers = {}, body } = config;
    
    const url = `${this.baseUrl}${endpoint}`;
    const requestHeaders = { ...this.defaultHeaders, ...headers };

    try {
      const response = await fetch(url, {
        method,
        headers: requestHeaders,
        body: body ? JSON.stringify(body) : undefined,
      });

      const data = await response.json();

      // Handle different response formats
      if (data.status === 'error') {
        // Standardized error response
        return {
          status: 'error',
          http_code: data.http_code || response.status,
          message: data.message || `HTTP error! status: ${response.status}`,
          data: data.data,
        };
      } else if (data.status === 'success') {
        // Standardized success response
        return {
          status: 'success',
          http_code: data.http_code || response.status,
          message: data.message || 'Operation completed successfully',
          data: data.data,
        };
      } else {
        // Legacy/direct response format (e.g., auth endpoints)
        return {
          status: 'success',
          http_code: response.status,
          message: data.message || 'Operation completed successfully',
          data: data,
        };
      }
    } catch (error) {
      console.error(`API Error (${method} ${url}):`, error);
      return {
        status: 'error',
        http_code: 0,
        message: error instanceof Error ? error.message : 'Network error occurred',
      };
    }
  }

  protected get<T>(endpoint: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET', headers });
  }

  protected post<T>(endpoint: string, body?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'POST', body, headers });
  }

  protected put<T>(endpoint: string, body?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'PUT', body, headers });
  }

  protected patch<T>(endpoint: string, body?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'PATCH', body, headers });
  }

  protected delete<T>(endpoint: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE', headers });
  }

  setAuthToken(token: string): void {
    this.defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  removeAuthToken(): void {
    delete this.defaultHeaders['Authorization'];
  }
}