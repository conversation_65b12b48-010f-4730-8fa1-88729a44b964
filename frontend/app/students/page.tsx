"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import { toast } from "@/hooks/use-toast"
import { Search, Plus, Filter, Download, Upload, Eye, Edit, Trash2, Users, UserPlus, Loader2 } from "lucide-react"
import Link from "next/link"
import { studentsService, type Student } from "@/services/students"

export default function StudentsPage() {
  const [students, setStudents] = useState<Student[]>([])
  const [filteredStudents, setFilteredStudents] = useState<Student[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [classFilter, setClassFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [totalCount, setTotalCount] = useState(0)
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    newThisMonth: 0,
    graduated: 0
  })

  const fetchStudents = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await studentsService.getStudents({
        limit: 100 // For now, get more students to populate the page
      })
      
      if (response.status === 'success' && response.data) {
        const studentsList = response.data.results || []
        setStudents(studentsList)
        setFilteredStudents(studentsList)
        setTotalCount(response.data.count || 0)
        
        // Calculate stats
        const activeCount = studentsList.filter(s => s.is_active).length
        const thisMonth = new Date()
        thisMonth.setMonth(thisMonth.getMonth())
        const newThisMonth = studentsList.filter(s => {
          const admissionDate = new Date(s.admission_date)
          return admissionDate.getMonth() === thisMonth.getMonth() && 
                 admissionDate.getFullYear() === thisMonth.getFullYear()
        }).length
        
        setStats({
          total: studentsList.length,
          active: activeCount,
          newThisMonth,
          graduated: studentsList.length - activeCount
        })
      } else {
        throw new Error(response.message || 'Failed to fetch students')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch students'
      setError(errorMessage)
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const filterStudents = () => {
    let filtered = students

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(student =>
        `${student.first_name} ${student.last_name}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.admission_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        `${student.class_level}-${student.section}`.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Apply class filter
    if (classFilter !== "all") {
      filtered = filtered.filter(student => student.class_level === classFilter)
    }

    // Apply status filter
    if (statusFilter !== "all") {
      if (statusFilter === "active") {
        filtered = filtered.filter(student => student.is_active)
      } else if (statusFilter === "inactive") {
        filtered = filtered.filter(student => !student.is_active)
      }
    }

    setFilteredStudents(filtered)
  }

  const handleDeleteStudent = async (id: number) => {
    if (!confirm('Are you sure you want to delete this student?')) return
    
    try {
      const response = await studentsService.deleteStudent(id)
      if (response.status === 'success') {
        toast({
          title: "Success",
          description: "Student deleted successfully",
        })
        fetchStudents() // Refresh the list
      } else {
        throw new Error(response.message || 'Failed to delete student')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete student'
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }

  useEffect(() => {
    fetchStudents()
  }, [])

  useEffect(() => {
    filterStudents()
  }, [students, searchTerm, classFilter, statusFilter])

  return (
    <div className="p-6 space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Student Management</h1>
          <p className="text-gray-600 mt-1">Manage student profiles, enrollment, and academic records</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            size="sm"
            className="border-indigo-200 text-indigo-700 hover:bg-indigo-50 bg-transparent"
          >
            <Upload className="h-4 w-4 mr-2" />
            Import Students
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="border-indigo-200 text-indigo-700 hover:bg-indigo-50 bg-transparent"
          >
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
          <Link href="/students/new">
            <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700">
              <UserPlus className="h-4 w-4 mr-2" />
              Add Student
            </Button>
          </Link>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-indigo-100">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-700">Total Students</p>
                {loading ? (
                  <Skeleton className="h-9 w-16 mt-2" />
                ) : (
                  <p className="text-3xl font-bold text-blue-900">{stats.total.toLocaleString()}</p>
                )}
              </div>
              <div className="p-3 bg-blue-500 rounded-full">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-emerald-100">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-700">Active Students</p>
                {loading ? (
                  <Skeleton className="h-9 w-16 mt-2" />
                ) : (
                  <p className="text-3xl font-bold text-green-900">{stats.active.toLocaleString()}</p>
                )}
              </div>
              <div className="p-3 bg-green-500 rounded-full">
                <UserPlus className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-amber-100">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-700">New This Month</p>
                {loading ? (
                  <Skeleton className="h-9 w-16 mt-2" />
                ) : (
                  <p className="text-3xl font-bold text-orange-900">{stats.newThisMonth}</p>
                )}
              </div>
              <div className="p-3 bg-orange-500 rounded-full">
                <Plus className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-indigo-100">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-700">Inactive</p>
                {loading ? (
                  <Skeleton className="h-9 w-16 mt-2" />
                ) : (
                  <p className="text-3xl font-bold text-purple-900">{stats.graduated}</p>
                )}
              </div>
              <div className="p-3 bg-purple-500 rounded-full">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card className="border-0 shadow-lg">
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search students by name, ID, or class..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
            <Select value={classFilter} onValueChange={setClassFilter}>
              <SelectTrigger className="w-48 border-gray-300">
                <SelectValue placeholder="Filter by Class" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Classes</SelectItem>
                <SelectItem value="1">Grade 1</SelectItem>
                <SelectItem value="2">Grade 2</SelectItem>
                <SelectItem value="3">Grade 3</SelectItem>
                <SelectItem value="4">Grade 4</SelectItem>
                <SelectItem value="5">Grade 5</SelectItem>
                <SelectItem value="6">Grade 6</SelectItem>
                <SelectItem value="7">Grade 7</SelectItem>
                <SelectItem value="8">Grade 8</SelectItem>
                <SelectItem value="9">Grade 9</SelectItem>
                <SelectItem value="10">Grade 10</SelectItem>
                <SelectItem value="11">Grade 11</SelectItem>
                <SelectItem value="12">Grade 12</SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48 border-gray-300">
                <SelectValue placeholder="Filter by Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" className="border-indigo-200 text-indigo-700 hover:bg-indigo-50 bg-transparent">
              <Filter className="h-4 w-4 mr-2" />
              More Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Students Table */}
      <Card className="border-0 shadow-lg">
        <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
          <CardTitle className="text-xl text-indigo-900">Students Directory</CardTitle>
          <CardDescription>{filteredStudents.length} students found</CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold text-gray-700">Student</TableHead>
                <TableHead className="font-semibold text-gray-700">Student ID</TableHead>
                <TableHead className="font-semibold text-gray-700">Class</TableHead>
                <TableHead className="font-semibold text-gray-700">Guardian</TableHead>
                <TableHead className="font-semibold text-gray-700">Contact</TableHead>
                <TableHead className="font-semibold text-gray-700">Status</TableHead>
                <TableHead className="font-semibold text-gray-700">Gender</TableHead>
                <TableHead className="font-semibold text-gray-700">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                // Loading skeleton rows
                Array.from({ length: 5 }).map((_, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Skeleton className="h-10 w-10 rounded-full" />
                        <div>
                          <Skeleton className="h-4 w-24 mb-1" />
                          <Skeleton className="h-3 w-20" />
                        </div>
                      </div>
                    </TableCell>
                    <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                    <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-28" /></TableCell>
                    <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                    <TableCell><Skeleton className="h-6 w-12" /></TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Skeleton className="h-8 w-8" />
                        <Skeleton className="h-8 w-8" />
                        <Skeleton className="h-8 w-8" />
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : filteredStudents.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                    {searchTerm || classFilter !== "all" || statusFilter !== "all" 
                      ? "No students match the current filters" 
                      : "No students found"
                    }
                  </TableCell>
                </TableRow>
              ) : (
                filteredStudents.map((student) => (
                  <TableRow key={student.id} className="hover:bg-gray-50 transition-colors">
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <img
                          src={`https://ui-avatars.com/api/?name=${encodeURIComponent(
                            `${student.first_name} ${student.last_name}`
                          )}&background=6366f1&color=ffffff&size=40`}
                          alt={`${student.first_name} ${student.last_name}`}
                          className="h-10 w-10 rounded-full object-cover"
                        />
                        <div>
                          <p className="font-medium text-gray-900">
                            {student.first_name} {student.last_name}
                          </p>
                          <p className="text-sm text-gray-500">
                            Joined {new Date(student.admission_date).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="font-mono text-sm">{student.admission_number}</TableCell>
                    <TableCell>
                      <Badge variant="outline" className="border-indigo-200 text-indigo-700">
                        Grade {student.class_level}-{student.section}
                      </Badge>
                    </TableCell>
                    <TableCell>{student.guardian_name || "N/A"}</TableCell>
                    <TableCell className="font-mono text-sm">
                      {student.phone_number || student.guardian_phone || "N/A"}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={student.is_active ? "default" : "secondary"}
                        className={student.is_active ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}
                      >
                        {student.is_active ? "Active" : "Inactive"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="border-gray-200 text-gray-700">
                        {student.gender === 'M' ? 'Male' : 'Female'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Link href={`/students/${student.id}`}>
                          <Button variant="ghost" size="sm" className="text-indigo-600 hover:bg-indigo-50">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Link href={`/students/${student.id}/edit`}>
                          <Button variant="ghost" size="sm" className="text-blue-600 hover:bg-blue-50">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="text-red-600 hover:bg-red-50"
                          onClick={() => student.id && handleDeleteStudent(student.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
