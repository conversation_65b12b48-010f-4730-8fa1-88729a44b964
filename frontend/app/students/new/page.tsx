"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Upload, Save, ArrowLeft, User, Users, GraduationCap, Heart, FileText } from "lucide-react"
import Link from "next/link"

export default function NewStudentPage() {
  const [activeTab, setActiveTab] = useState("basic")

  return (
    <div className="p-6 space-y-6">
      {/* <PERSON> Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/students">
            <Button variant="ghost" size="sm" className="text-indigo-600 hover:bg-indigo-50">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Students
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Add New Student</h1>
            <p className="text-gray-600 mt-1">Create a comprehensive student profile with all required information</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" className="border-indigo-200 text-indigo-700 hover:bg-indigo-50 bg-transparent">
            Save as Draft
          </Button>
          <Button className="bg-indigo-600 hover:bg-indigo-700">
            <Save className="h-4 w-4 mr-2" />
            Save Student
          </Button>
        </div>
      </div>

      <div className="max-w-6xl mx-auto">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <TabsList className="grid w-full grid-cols-5 bg-indigo-50">
              <TabsTrigger value="basic" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
                <User className="h-4 w-4 mr-2" />
                Basic Info
              </TabsTrigger>
              <TabsTrigger
                value="guardian"
                className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white"
              >
                <Users className="h-4 w-4 mr-2" />
                Guardian
              </TabsTrigger>
              <TabsTrigger
                value="academic"
                className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white"
              >
                <GraduationCap className="h-4 w-4 mr-2" />
                Academic
              </TabsTrigger>
              <TabsTrigger value="medical" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
                <Heart className="h-4 w-4 mr-2" />
                Medical
              </TabsTrigger>
              <TabsTrigger
                value="documents"
                className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white"
              >
                <FileText className="h-4 w-4 mr-2" />
                Documents
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="basic" className="space-y-6">
            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
                <CardTitle className="text-xl text-indigo-900 flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  Basic Information
                </CardTitle>
                <CardDescription>Student personal details and identification information</CardDescription>
              </CardHeader>
              <CardContent className="p-8 space-y-8">
                {/* Photo Upload Section */}
                <div className="flex items-center space-x-8">
                  <Avatar className="h-32 w-32 border-4 border-indigo-100">
                    <AvatarImage src="/placeholder.svg?height=128&width=128" />
                    <AvatarFallback className="bg-indigo-100 text-indigo-600 text-2xl">ST</AvatarFallback>
                  </Avatar>
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Student Photo</h3>
                      <p className="text-sm text-gray-600">Upload a clear photo of the student for identification</p>
                    </div>
                    <Button
                      variant="outline"
                      className="border-indigo-200 text-indigo-700 hover:bg-indigo-50 bg-transparent"
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Photo
                    </Button>
                    <p className="text-xs text-gray-500">JPG, PNG up to 2MB. Recommended size: 400x400px</p>
                  </div>
                </div>

                {/* Personal Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="space-y-2">
                    <Label htmlFor="firstName" className="text-sm font-semibold text-gray-700">
                      First Name *
                    </Label>
                    <Input
                      id="firstName"
                      placeholder="Enter first name"
                      className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName" className="text-sm font-semibold text-gray-700">
                      Last Name *
                    </Label>
                    <Input
                      id="lastName"
                      placeholder="Enter last name"
                      className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="studentId" className="text-sm font-semibold text-gray-700">
                      Student ID
                    </Label>
                    <Input id="studentId" placeholder="STU-2024-XXX (Auto-generated)" disabled className="bg-gray-50" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="dateOfBirth" className="text-sm font-semibold text-gray-700">
                      Date of Birth *
                    </Label>
                    <Input
                      id="dateOfBirth"
                      type="date"
                      className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="gender" className="text-sm font-semibold text-gray-700">
                      Gender *
                    </Label>
                    <Select>
                      <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                        <SelectValue placeholder="Select gender" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="male">Male</SelectItem>
                        <SelectItem value="female">Female</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="nationality" className="text-sm font-semibold text-gray-700">
                      Nationality
                    </Label>
                    <Input
                      id="nationality"
                      placeholder="Enter nationality"
                      className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="cnic" className="text-sm font-semibold text-gray-700">
                      CNIC/ID Number
                    </Label>
                    <Input
                      id="cnic"
                      placeholder="Enter CNIC or ID number"
                      className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="religion" className="text-sm font-semibold text-gray-700">
                      Religion
                    </Label>
                    <Input
                      id="religion"
                      placeholder="Enter religion"
                      className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address" className="text-sm font-semibold text-gray-700">
                    Complete Address
                  </Label>
                  <Textarea
                    id="address"
                    placeholder="Enter complete residential address"
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 min-h-[100px]"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="guardian" className="space-y-6">
            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
                <CardTitle className="text-xl text-indigo-900 flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  Guardian Information
                </CardTitle>
                <CardDescription>Primary and secondary guardian contact details</CardDescription>
              </CardHeader>
              <CardContent className="p-8 space-y-8">
                {/* Primary Guardian */}
                <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
                  <h3 className="text-lg font-semibold text-blue-900 mb-6">Primary Guardian</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="primaryName" className="text-sm font-semibold text-gray-700">
                        Full Name *
                      </Label>
                      <Input
                        id="primaryName"
                        placeholder="Enter guardian full name"
                        className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="primaryRelation" className="text-sm font-semibold text-gray-700">
                        Relationship *
                      </Label>
                      <Select>
                        <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                          <SelectValue placeholder="Select relationship" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="father">Father</SelectItem>
                          <SelectItem value="mother">Mother</SelectItem>
                          <SelectItem value="guardian">Legal Guardian</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="primaryPhone" className="text-sm font-semibold text-gray-700">
                        Phone Number *
                      </Label>
                      <Input
                        id="primaryPhone"
                        placeholder="+92-XXX-XXXXXXX"
                        className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="primaryEmail" className="text-sm font-semibold text-gray-700">
                        Email Address
                      </Label>
                      <Input
                        id="primaryEmail"
                        type="email"
                        placeholder="<EMAIL>"
                        className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="primaryOccupation" className="text-sm font-semibold text-gray-700">
                        Occupation
                      </Label>
                      <Input
                        id="primaryOccupation"
                        placeholder="Enter occupation"
                        className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="primaryCnic" className="text-sm font-semibold text-gray-700">
                        CNIC Number
                      </Label>
                      <Input
                        id="primaryCnic"
                        placeholder="XXXXX-XXXXXXX-X"
                        className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                      />
                    </div>
                  </div>
                </div>

                {/* Secondary Guardian */}
                <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-6">Secondary Guardian (Optional)</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="secondaryName" className="text-sm font-semibold text-gray-700">
                        Full Name
                      </Label>
                      <Input
                        id="secondaryName"
                        placeholder="Enter guardian full name"
                        className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="secondaryRelation" className="text-sm font-semibold text-gray-700">
                        Relationship
                      </Label>
                      <Select>
                        <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                          <SelectValue placeholder="Select relationship" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="father">Father</SelectItem>
                          <SelectItem value="mother">Mother</SelectItem>
                          <SelectItem value="guardian">Legal Guardian</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="secondaryPhone" className="text-sm font-semibold text-gray-700">
                        Phone Number
                      </Label>
                      <Input
                        id="secondaryPhone"
                        placeholder="+92-XXX-XXXXXXX"
                        className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="secondaryEmail" className="text-sm font-semibold text-gray-700">
                        Email Address
                      </Label>
                      <Input
                        id="secondaryEmail"
                        type="email"
                        placeholder="<EMAIL>"
                        className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="academic" className="space-y-6">
            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
                <CardTitle className="text-xl text-indigo-900 flex items-center">
                  <GraduationCap className="h-5 w-5 mr-2" />
                  Academic Information
                </CardTitle>
                <CardDescription>Class assignment and academic enrollment details</CardDescription>
              </CardHeader>
              <CardContent className="p-8 space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="space-y-2">
                    <Label htmlFor="academicYear" className="text-sm font-semibold text-gray-700">
                      Academic Year *
                    </Label>
                    <Select>
                      <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                        <SelectValue placeholder="Select academic year" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="2024-2025">2024-2025</SelectItem>
                        <SelectItem value="2023-2024">2023-2024</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="term" className="text-sm font-semibold text-gray-700">
                      Term/Semester *
                    </Label>
                    <Select>
                      <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                        <SelectValue placeholder="Select term" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="fall-2024">Fall 2024</SelectItem>
                        <SelectItem value="spring-2024">Spring 2024</SelectItem>
                        <SelectItem value="summer-2024">Summer 2024</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="grade" className="text-sm font-semibold text-gray-700">
                      Grade/Class *
                    </Label>
                    <Select>
                      <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                        <SelectValue placeholder="Select grade" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="grade-9">Grade 9</SelectItem>
                        <SelectItem value="grade-10">Grade 10</SelectItem>
                        <SelectItem value="grade-11">Grade 11</SelectItem>
                        <SelectItem value="grade-12">Grade 12</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="section" className="text-sm font-semibold text-gray-700">
                      Section *
                    </Label>
                    <Select>
                      <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                        <SelectValue placeholder="Select section" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="a">Section A</SelectItem>
                        <SelectItem value="b">Section B</SelectItem>
                        <SelectItem value="c">Section C</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="rollNumber" className="text-sm font-semibold text-gray-700">
                      Roll Number
                    </Label>
                    <Input
                      id="rollNumber"
                      placeholder="Enter roll number"
                      className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="admissionDate" className="text-sm font-semibold text-gray-700">
                      Admission Date *
                    </Label>
                    <Input
                      id="admissionDate"
                      type="date"
                      className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="previousSchool" className="text-sm font-semibold text-gray-700">
                    Previous School (if any)
                  </Label>
                  <Input
                    id="previousSchool"
                    placeholder="Enter previous school name and details"
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="medical" className="space-y-6">
            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
                <CardTitle className="text-xl text-indigo-900 flex items-center">
                  <Heart className="h-5 w-5 mr-2" />
                  Medical Information
                </CardTitle>
                <CardDescription>Health records and medical details for student safety</CardDescription>
              </CardHeader>
              <CardContent className="p-8 space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="space-y-2">
                    <Label htmlFor="bloodGroup" className="text-sm font-semibold text-gray-700">
                      Blood Group
                    </Label>
                    <Select>
                      <SelectTrigger className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500">
                        <SelectValue placeholder="Select blood group" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="a+">A+</SelectItem>
                        <SelectItem value="a-">A-</SelectItem>
                        <SelectItem value="b+">B+</SelectItem>
                        <SelectItem value="b-">B-</SelectItem>
                        <SelectItem value="ab+">AB+</SelectItem>
                        <SelectItem value="ab-">AB-</SelectItem>
                        <SelectItem value="o+">O+</SelectItem>
                        <SelectItem value="o-">O-</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="height" className="text-sm font-semibold text-gray-700">
                      Height (cm)
                    </Label>
                    <Input
                      id="height"
                      type="number"
                      placeholder="Enter height in centimeters"
                      className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="weight" className="text-sm font-semibold text-gray-700">
                      Weight (kg)
                    </Label>
                    <Input
                      id="weight"
                      type="number"
                      placeholder="Enter weight in kilograms"
                      className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="emergencyContact" className="text-sm font-semibold text-gray-700">
                      Emergency Contact
                    </Label>
                    <Input
                      id="emergencyContact"
                      placeholder="Emergency contact number"
                      className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>
                </div>

                <div className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="allergies" className="text-sm font-semibold text-gray-700">
                      Known Allergies
                    </Label>
                    <Textarea
                      id="allergies"
                      placeholder="List any known allergies (food, medication, environmental, etc.)"
                      className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 min-h-[80px]"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="medications" className="text-sm font-semibold text-gray-700">
                      Current Medications
                    </Label>
                    <Textarea
                      id="medications"
                      placeholder="List current medications and dosages"
                      className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 min-h-[80px]"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="medicalConditions" className="text-sm font-semibold text-gray-700">
                      Medical Conditions
                    </Label>
                    <Textarea
                      id="medicalConditions"
                      placeholder="List any chronic conditions, disabilities, or special medical needs"
                      className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 min-h-[80px]"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents" className="space-y-6">
            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b">
                <CardTitle className="text-xl text-indigo-900 flex items-center">
                  <FileText className="h-5 w-5 mr-2" />
                  Document Management
                </CardTitle>
                <CardDescription>Upload and manage required student documents</CardDescription>
              </CardHeader>
              <CardContent className="p-8 space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="space-y-4">
                    <h3 className="font-semibold text-gray-900 flex items-center">
                      <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                      Identity Documents
                    </h3>
                    <div className="border-2 border-dashed border-indigo-300 rounded-lg p-8 text-center bg-indigo-50 hover:bg-indigo-100 transition-colors">
                      <Upload className="mx-auto h-12 w-12 text-indigo-400" />
                      <div className="mt-4">
                        <Button
                          variant="outline"
                          className="border-indigo-300 text-indigo-700 hover:bg-indigo-100 bg-transparent"
                        >
                          Upload Birth Certificate
                        </Button>
                        <p className="text-sm text-gray-600 mt-2">PDF, JPG, PNG up to 5MB</p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="font-semibold text-gray-900 flex items-center">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                      Academic Documents
                    </h3>
                    <div className="border-2 border-dashed border-indigo-300 rounded-lg p-8 text-center bg-indigo-50 hover:bg-indigo-100 transition-colors">
                      <Upload className="mx-auto h-12 w-12 text-indigo-400" />
                      <div className="mt-4">
                        <Button
                          variant="outline"
                          className="border-indigo-300 text-indigo-700 hover:bg-indigo-100 bg-transparent"
                        >
                          Upload Previous Transcripts
                        </Button>
                        <p className="text-sm text-gray-600 mt-2">PDF, JPG, PNG up to 5MB</p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="font-semibold text-gray-900 flex items-center">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                      Medical Documents
                    </h3>
                    <div className="border-2 border-dashed border-indigo-300 rounded-lg p-8 text-center bg-indigo-50 hover:bg-indigo-100 transition-colors">
                      <Upload className="mx-auto h-12 w-12 text-indigo-400" />
                      <div className="mt-4">
                        <Button
                          variant="outline"
                          className="border-indigo-300 text-indigo-700 hover:bg-indigo-100 bg-transparent"
                        >
                          Upload Medical Records
                        </Button>
                        <p className="text-sm text-gray-600 mt-2">PDF, JPG, PNG up to 5MB</p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="font-semibold text-gray-900 flex items-center">
                      <div className="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
                      Financial Documents
                    </h3>
                    <div className="border-2 border-dashed border-indigo-300 rounded-lg p-8 text-center bg-indigo-50 hover:bg-indigo-100 transition-colors">
                      <Upload className="mx-auto h-12 w-12 text-indigo-400" />
                      <div className="mt-4">
                        <Button
                          variant="outline"
                          className="border-indigo-300 text-indigo-700 hover:bg-indigo-100 bg-transparent"
                        >
                          Upload Financial Documents
                        </Button>
                        <p className="text-sm text-gray-600 mt-2">PDF, JPG, PNG up to 5MB</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
